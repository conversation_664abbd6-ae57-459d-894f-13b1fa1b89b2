'use client'

import { useState, useEffect } from 'react'
import { Search, Filter, X, Calendar, DollarSign, MapPin, Tag, Building, Clock, Shield, FileText, Globe, Zap } from 'lucide-react'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { TenderFilters, tenderApi } from '@/lib/supabase'
import { debounce } from '@/lib/utils'

interface SearchFiltersProps {
  filters: TenderFilters
  onFiltersChange: (filters: TenderFilters) => void
  loading?: boolean
  className?: string
}

export function SearchFilters({
  filters,
  onFiltersChange,
  loading = false,
  className
}: SearchFiltersProps) {
  const [localFilters, setLocalFilters] = useState<TenderFilters>(filters)
  const [showAdvanced, setShowAdvanced] = useState(false)
  const [filterOptions, setFilterOptions] = useState({
    provinces: [] as string[],
    categories: [] as string[],
    statuses: [] as string[],
    opportunityTypes: [] as { value: string; label: string; count: number; description: string }[],
    tenderTypes: [] as string[],
    procurementMethods: [] as string[],
    issuerTypes: [] as string[],
    urgencyLevels: [] as string[],
    valueRanges: [] as { label: string; min: number; max?: number }[],
    industries: [] as string[],
    geographicScopes: [] as string[],
    documentStatuses: [] as string[]
  })

  // Debounced search
  const debouncedSearch = debounce((searchTerm: string) => {
    onFiltersChange({ ...localFilters, search: searchTerm })
  }, 500)

  // Load filter options
  useEffect(() => {
    const loadOptions = async () => {
      try {
        const options = await tenderApi.getFilterOptions()
        setFilterOptions(options)
      } catch (error) {
        console.error('Error loading filter options:', error)
      }
    }
    loadOptions()
  }, [])

  // Handle search input
  const handleSearchChange = (value: string) => {
    setLocalFilters(prev => ({ ...prev, search: value }))
    debouncedSearch(value)
  }

  // Handle filter changes
  const handleFilterChange = (key: keyof TenderFilters, value: any) => {
    const newFilters = { ...localFilters, [key]: value }
    setLocalFilters(newFilters)
    onFiltersChange(newFilters)
  }

  // Clear all filters
  const clearFilters = () => {
    const clearedFilters: TenderFilters = {
      search: '',
      sortBy: 'publish_date', // Default to date advertised (publish_date)
      sortOrder: 'desc'
    }
    setLocalFilters(clearedFilters)
    onFiltersChange(clearedFilters)
  }

  // Count active filters
  const activeFiltersCount = Object.entries(localFilters).filter(([key, value]) => {
    if (key === 'sortBy' || key === 'sortOrder') return false
    return value && value !== ''
  }).length

  return (
    <div className={className}>
      {/* BidBeez Opportunity Type Selector */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-3">🎯 Choose Your Opportunity Type</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {filterOptions.opportunityTypes.map((type) => (
            <button
              key={type.value}
              onClick={() => handleFilterChange('opportunityType', type.value)}
              className={`p-4 rounded-lg border-2 transition-all duration-200 text-left ${
                (localFilters.opportunityType || 'all') === type.value
                  ? 'border-blue-500 bg-blue-50 shadow-md'
                  : 'border-gray-200 hover:border-blue-300 hover:bg-blue-25'
              }`}
              disabled={loading}
            >
              <div className="flex items-center justify-between mb-2">
                <h4 className="font-semibold text-gray-900">{type.label}</h4>
                <Badge className={`${
                  type.value === 'rfq' ? 'bg-green-100 text-green-700' :
                  type.value === 'tender' ? 'bg-blue-100 text-blue-700' :
                  type.value === 'direct_appointment' ? 'bg-purple-100 text-purple-700' :
                  'bg-gray-100 text-gray-700'
                }`}>
                  {type.count}
                </Badge>
              </div>
              <p className="text-sm text-gray-600">{type.description}</p>
              {type.value === 'rfq' && (
                <div className="mt-2 flex items-center space-x-1">
                  <span className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded-full font-medium">
                    🍃 Low Hanging Fruit
                  </span>
                </div>
              )}
            </button>
          ))}
        </div>
      </div>

      {/* Current Status Indicator */}
      <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-lg">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <span className="text-lg">🔴</span>
            <div>
              <h4 className="font-semibold text-green-800">
                {localFilters.status === 'Live/Open' ? 'Showing Live Opportunities' :
                 localFilters.status === 'Closed' ? 'Showing Closed Opportunities' :
                 localFilters.status === 'Closing Soon' ? 'Showing Opportunities Closing Soon' :
                 'Showing All Opportunities'}
              </h4>
              <p className="text-sm text-green-600">
                {localFilters.status === 'Live/Open' ? 'These tenders are open for bidding - you can submit proposals' :
                 localFilters.status === 'Closed' ? 'These tenders have closed - bidding has ended' :
                 localFilters.status === 'Closing Soon' ? 'These tenders close within 7 days - act fast!' :
                 'All tenders regardless of status'}
              </p>
            </div>
          </div>
          {localFilters.status !== 'Live/Open' && (
            <Button
              onClick={() => handleFilterChange('status', 'Live/Open')}
              size="sm"
              className="bg-green-600 hover:bg-green-700"
            >
              Show Live Opportunities
            </Button>
          )}
        </div>
      </div>

      {/* Enhanced Main Search */}
      <div className="bg-white rounded-xl border border-gray-200 shadow-lg p-4 mb-6">
        <div className="flex flex-col lg:flex-row items-stretch lg:items-center space-y-3 lg:space-y-0 lg:space-x-4">
          <div className="relative flex-1">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
            <Input
              placeholder="🔍 Search tenders by title, description, issuer, reference number, or keywords..."
              value={localFilters.search || ''}
              onChange={(e) => handleSearchChange(e.target.value)}
              className="pl-12 pr-4 py-3 text-base border-gray-300 focus:border-blue-500 focus:ring-blue-500 rounded-lg shadow-sm"
              disabled={loading}
            />
            {localFilters.search && (
              <button
                onClick={() => handleFilterChange('search', '')}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
              >
                <X className="w-4 h-4" />
              </button>
            )}
          </div>

          <div className="flex items-center space-x-3">
            <Button
              variant="outline"
              onClick={() => setShowAdvanced(!showAdvanced)}
              className="flex items-center space-x-2 px-4 py-3 bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200 hover:from-blue-100 hover:to-indigo-100 text-blue-700 font-medium rounded-lg shadow-sm transition-all duration-200"
              disabled={loading}
            >
              <Filter className="w-4 h-4" />
              <span>Advanced Filters</span>
              {activeFiltersCount > 0 && (
                <Badge variant="default" className="ml-1 bg-blue-600 text-white">
                  {activeFiltersCount}
                </Badge>
              )}
            </Button>

            {activeFiltersCount > 0 && (
              <Button
                variant="ghost"
                onClick={clearFilters}
                className="flex items-center space-x-2 px-4 py-3 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-all duration-200"
                disabled={loading}
              >
                <X className="w-4 h-4" />
                <span>Clear All</span>
              </Button>
            )}
          </div>
        </div>
      </div>

      {/* Advanced Filters */}
      {showAdvanced && (
        <div className="bg-gradient-to-br from-gray-50 to-white rounded-xl p-6 space-y-8 border border-gray-200 shadow-lg">
          {/* Primary Filters */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Primary Filters</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {/* Province Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <MapPin className="w-4 h-4 inline mr-1" />
                  Province
                </label>
                <select
                  value={localFilters.province || ''}
                  onChange={(e) => handleFilterChange('province', e.target.value)}
                  className="w-full border-gray-300 focus:border-blue-500 focus:ring-blue-500 rounded-md px-3 py-2"
                  disabled={loading}
                >
                  <option value="">All Provinces</option>
                  {filterOptions.provinces.map(province => (
                    <option key={province} value={province}>{province}</option>
                  ))}
                </select>
              </div>

              {/* Status Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <Zap className="w-4 h-4 inline mr-1" />
                  Bidding Status
                </label>
                <select
                  value={localFilters.status || 'Live/Open'}
                  onChange={(e) => handleFilterChange('status', e.target.value)}
                  className="w-full border-gray-300 focus:border-blue-500 focus:ring-blue-500 rounded-md px-3 py-2"
                  disabled={loading}
                >
                  {filterOptions.statuses.map(status => (
                    <option key={status} value={status}>
                      {status === 'Live/Open' ? '🔴 Live/Open (Can Bid)' :
                       status === 'Closed' ? '⚫ Closed (Bidding Ended)' :
                       status === 'Closing Soon' ? '🟡 Closing Soon (< 7 days)' :
                       status}
                    </option>
                  ))}
                </select>
              </div>

              {/* Category Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <Tag className="w-4 h-4 inline mr-1" />
                  Category
                </label>
                <select
                  value={localFilters.category || ''}
                  onChange={(e) => handleFilterChange('category', e.target.value)}
                  className="w-full border-gray-300 focus:border-blue-500 focus:ring-blue-500 rounded-md px-3 py-2"
                  disabled={loading}
                >
                  <option value="">All Categories</option>
                  {filterOptions.categories.map(category => (
                    <option key={category} value={category}>{category}</option>
                  ))}
                </select>
              </div>

              {/* Industry Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <Building className="w-4 h-4 inline mr-1" />
                  Industry
                </label>
                <select
                  value={localFilters.industry || ''}
                  onChange={(e) => handleFilterChange('industry', e.target.value)}
                  className="w-full border-gray-300 focus:border-blue-500 focus:ring-blue-500 rounded-md px-3 py-2"
                  disabled={loading}
                >
                  <option value="">All Industries</option>
                  {filterOptions.industries.map(industry => (
                    <option key={industry} value={industry}>{industry}</option>
                  ))}
                </select>
              </div>
            </div>
          </div>

          {/* Tender Details */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Tender Details</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {/* Tender Type */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <FileText className="w-4 h-4 inline mr-1" />
                  Tender Type
                </label>
                <select
                  value={localFilters.tenderType || ''}
                  onChange={(e) => handleFilterChange('tenderType', e.target.value)}
                  className="w-full border-gray-300 focus:border-blue-500 focus:ring-blue-500 rounded-md px-3 py-2"
                  disabled={loading}
                >
                  <option value="">All Types</option>
                  {filterOptions.tenderTypes.map(type => (
                    <option key={type} value={type}>{type}</option>
                  ))}
                </select>
              </div>

              {/* Procurement Method */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Procurement Method
                </label>
                <select
                  value={localFilters.procurementMethod || ''}
                  onChange={(e) => handleFilterChange('procurementMethod', e.target.value)}
                  className="w-full border-gray-300 focus:border-blue-500 focus:ring-blue-500 rounded-md px-3 py-2"
                  disabled={loading}
                >
                  <option value="">All Methods</option>
                  {filterOptions.procurementMethods.map(method => (
                    <option key={method} value={method}>{method}</option>
                  ))}
                </select>
              </div>

              {/* Issuer Type */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <Building className="w-4 h-4 inline mr-1" />
                  Issuer Type
                </label>
                <select
                  value={localFilters.issuerType || ''}
                  onChange={(e) => handleFilterChange('issuerType', e.target.value)}
                  className="w-full border-gray-300 focus:border-blue-500 focus:ring-blue-500 rounded-md px-3 py-2"
                  disabled={loading}
                >
                  <option value="">All Issuers</option>
                  {filterOptions.issuerTypes.map(issuer => (
                    <option key={issuer} value={issuer}>{issuer}</option>
                  ))}
                </select>
              </div>
            </div>
          </div>

          {/* Requirements & Compliance */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Requirements & Compliance</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {/* CIDB Required */}
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="cidbRequired"
                  checked={localFilters.cidbRequired || false}
                  onChange={(e) => handleFilterChange('cidbRequired', e.target.checked)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  disabled={loading}
                />
                <label htmlFor="cidbRequired" className="text-sm font-medium text-gray-700">
                  <Shield className="w-4 h-4 inline mr-1" />
                  CIDB Required
                </label>
              </div>

              {/* BEE Required */}
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="beeRequired"
                  checked={localFilters.beeRequired || false}
                  onChange={(e) => handleFilterChange('beeRequired', e.target.checked)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  disabled={loading}
                />
                <label htmlFor="beeRequired" className="text-sm font-medium text-gray-700">
                  BEE Required
                </label>
              </div>

              {/* Security Clearance */}
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="securityClearance"
                  checked={localFilters.securityClearance || false}
                  onChange={(e) => handleFilterChange('securityClearance', e.target.checked)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  disabled={loading}
                />
                <label htmlFor="securityClearance" className="text-sm font-medium text-gray-700">
                  Security Clearance
                </label>
              </div>

              {/* Geographic Scope */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <Globe className="w-4 h-4 inline mr-1" />
                  Scope
                </label>
                <select
                  value={localFilters.geographicScope || ''}
                  onChange={(e) => handleFilterChange('geographicScope', e.target.value)}
                  className="w-full border-gray-300 focus:border-blue-500 focus:ring-blue-500 rounded-md px-3 py-2"
                  disabled={loading}
                >
                  <option value="">All Scopes</option>
                  {filterOptions.geographicScopes.map(scope => (
                    <option key={scope} value={scope}>{scope}</option>
                  ))}
                </select>
              </div>
            </div>
          </div>

          {/* Value & Timing */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Value & Timing</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {/* Value Range */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <DollarSign className="w-4 h-4 inline mr-1" />
                  Value Range
                </label>
                <select
                  value={localFilters.valueRange || ''}
                  onChange={(e) => handleFilterChange('valueRange', e.target.value)}
                  className="w-full border-gray-300 focus:border-blue-500 focus:ring-blue-500 rounded-md px-3 py-2"
                  disabled={loading}
                >
                  <option value="">All Values</option>
                  {filterOptions.valueRanges.map(range => (
                    <option key={range.label} value={range.label}>{range.label}</option>
                  ))}
                </select>
              </div>

              {/* Urgency Level */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <Clock className="w-4 h-4 inline mr-1" />
                  Urgency
                </label>
                <select
                  value={localFilters.urgencyLevel || ''}
                  onChange={(e) => handleFilterChange('urgencyLevel', e.target.value)}
                  className="w-full border-gray-300 focus:border-blue-500 focus:ring-blue-500 rounded-md px-3 py-2"
                  disabled={loading}
                >
                  <option value="">All Urgency Levels</option>
                  {filterOptions.urgencyLevels.map(level => (
                    <option key={level} value={level}>{level}</option>
                  ))}
                </select>
              </div>

              {/* Sort By */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Sort By
                </label>
                <select
                  value={localFilters.sortBy || 'publish_date'}
                  onChange={(e) => handleFilterChange('sortBy', e.target.value)}
                  className="w-full border-gray-300 focus:border-blue-500 focus:ring-blue-500 rounded-md px-3 py-2"
                  disabled={loading}
                >
                  <option value="publish_date">📅 Date Advertised (Default)</option>
                  <option value="closing_date">⏰ Closing Date</option>
                  <option value="tender_value">💰 Value</option>
                  <option value="title">📝 Title</option>
                  <option value="created_at">🆕 Latest Added</option>
                  <option value="site_meeting_date">🏢 Site Meeting Date</option>
                </select>
              </div>
            </div>
          </div>

          {/* Custom Value Range */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Custom Value Range</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <DollarSign className="w-4 h-4 inline mr-1" />
                  Minimum Value (ZAR)
                </label>
                <Input
                  type="number"
                  placeholder="e.g., 100000"
                  value={localFilters.minValue || ''}
                  onChange={(e) => handleFilterChange('minValue', e.target.value ? parseFloat(e.target.value) : undefined)}
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500 rounded-md"
                  disabled={loading}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Maximum Value (ZAR)
                </label>
                <Input
                  type="number"
                  placeholder="e.g., 10000000"
                  value={localFilters.maxValue || ''}
                  onChange={(e) => handleFilterChange('maxValue', e.target.value ? parseFloat(e.target.value) : undefined)}
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500 rounded-md"
                  disabled={loading}
                />
              </div>
            </div>
          </div>

          {/* Enhanced Date Ranges */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">📅 Date Filters</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">

              {/* Advertised Date Range */}
              <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
                <h4 className="font-semibold text-gray-800 mb-3 flex items-center">
                  <Calendar className="w-4 h-4 mr-2 text-blue-500" />
                  Date Advertised Range
                </h4>
                <div className="space-y-3">
                  <div>
                    <label className="block text-xs font-medium text-gray-600 mb-1">From</label>
                    <Input
                      type="date"
                      value={localFilters.publishDateFrom || ''}
                      onChange={(e) => handleFilterChange('publishDateFrom', e.target.value)}
                      className="w-full text-sm border-gray-300 focus:border-blue-500 focus:ring-blue-500 rounded-md"
                      disabled={loading}
                    />
                  </div>
                  <div>
                    <label className="block text-xs font-medium text-gray-600 mb-1">To</label>
                    <Input
                      type="date"
                      value={localFilters.publishDateTo || ''}
                      onChange={(e) => handleFilterChange('publishDateTo', e.target.value)}
                      className="w-full text-sm border-gray-300 focus:border-blue-500 focus:ring-blue-500 rounded-md"
                      disabled={loading}
                    />
                  </div>
                </div>
              </div>

              {/* Closing Date Range */}
              <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
                <h4 className="font-semibold text-gray-800 mb-3 flex items-center">
                  <Clock className="w-4 h-4 mr-2 text-red-500" />
                  Closing Date Range
                </h4>
                <div className="space-y-3">
                  <div>
                    <label className="block text-xs font-medium text-gray-600 mb-1">From</label>
                    <Input
                      type="date"
                      value={localFilters.closingDateFrom || ''}
                      onChange={(e) => handleFilterChange('closingDateFrom', e.target.value)}
                      className="w-full text-sm border-gray-300 focus:border-red-500 focus:ring-red-500 rounded-md"
                      disabled={loading}
                    />
                  </div>
                  <div>
                    <label className="block text-xs font-medium text-gray-600 mb-1">To</label>
                    <Input
                      type="date"
                      value={localFilters.closingDateTo || ''}
                      onChange={(e) => handleFilterChange('closingDateTo', e.target.value)}
                      className="w-full text-sm border-gray-300 focus:border-red-500 focus:ring-red-500 rounded-md"
                      disabled={loading}
                    />
                  </div>
                </div>
              </div>

              {/* Site Meeting Date Range */}
              <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
                <h4 className="font-semibold text-gray-800 mb-3 flex items-center">
                  <MapPin className="w-4 h-4 mr-2 text-green-500" />
                  Site Meeting Date Range
                </h4>
                <div className="space-y-3">
                  <div>
                    <label className="block text-xs font-medium text-gray-600 mb-1">From</label>
                    <Input
                      type="date"
                      value={localFilters.siteMeetingDateFrom || ''}
                      onChange={(e) => handleFilterChange('siteMeetingDateFrom', e.target.value)}
                      className="w-full text-sm border-gray-300 focus:border-green-500 focus:ring-green-500 rounded-md"
                      disabled={loading}
                    />
                  </div>
                  <div>
                    <label className="block text-xs font-medium text-gray-600 mb-1">To</label>
                    <Input
                      type="date"
                      value={localFilters.siteMeetingDateTo || ''}
                      onChange={(e) => handleFilterChange('siteMeetingDateTo', e.target.value)}
                      className="w-full text-sm border-gray-300 focus:border-green-500 focus:ring-green-500 rounded-md"
                      disabled={loading}
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Quick Date Presets */}
            <div className="mt-4">
              <h4 className="text-sm font-medium text-gray-700 mb-2">Quick Date Filters</h4>
              <div className="flex flex-wrap gap-2">
                {[
                  { label: 'Today', days: 0 },
                  { label: 'This Week', days: 7 },
                  { label: 'This Month', days: 30 },
                  { label: 'Next 7 Days', days: 7, future: true },
                  { label: 'Next 30 Days', days: 30, future: true },
                ].map((preset) => (
                  <button
                    key={preset.label}
                    onClick={() => {
                      const today = new Date()
                      const targetDate = new Date()

                      if (preset.future) {
                        targetDate.setDate(today.getDate() + preset.days)
                        handleFilterChange('closingDateFrom', today.toISOString().split('T')[0])
                        handleFilterChange('closingDateTo', targetDate.toISOString().split('T')[0])
                      } else {
                        targetDate.setDate(today.getDate() - preset.days)
                        handleFilterChange('publishDateFrom', targetDate.toISOString().split('T')[0])
                        handleFilterChange('publishDateTo', today.toISOString().split('T')[0])
                      }
                    }}
                    className="px-3 py-1 text-xs bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-full transition-colors"
                    disabled={loading}
                  >
                    {preset.label}
                  </button>
                ))}
                <button
                  onClick={() => {
                    handleFilterChange('publishDateFrom', '')
                    handleFilterChange('publishDateTo', '')
                    handleFilterChange('closingDateFrom', '')
                    handleFilterChange('closingDateTo', '')
                    handleFilterChange('siteMeetingDateFrom', '')
                    handleFilterChange('siteMeetingDateTo', '')
                  }}
                  className="px-3 py-1 text-xs bg-red-100 hover:bg-red-200 text-red-700 rounded-full transition-colors"
                  disabled={loading}
                >
                  Clear Dates
                </button>
              </div>
            </div>
          </div>

          {/* Advanced Search Options */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">🔍 Advanced Search Options</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">

              {/* Site Meeting Required */}
              <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="siteMeetingRequired"
                    checked={localFilters.siteMeetingRequired || false}
                    onChange={(e) => handleFilterChange('siteMeetingRequired', e.target.checked)}
                    className="rounded border-gray-300 text-green-600 focus:ring-green-500"
                    disabled={loading}
                  />
                  <label htmlFor="siteMeetingRequired" className="text-sm font-medium text-gray-700">
                    <MapPin className="w-4 h-4 inline mr-1" />
                    Site Meeting Required
                  </label>
                </div>
              </div>

              {/* Document Available */}
              <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="documentAvailable"
                    checked={localFilters.documentAvailable || false}
                    onChange={(e) => handleFilterChange('documentAvailable', e.target.checked)}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    disabled={loading}
                  />
                  <label htmlFor="documentAvailable" className="text-sm font-medium text-gray-700">
                    <FileText className="w-4 h-4 inline mr-1" />
                    Documents Available
                  </label>
                </div>
              </div>

              {/* High Value Tenders */}
              <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="highValue"
                    checked={localFilters.highValue || false}
                    onChange={(e) => handleFilterChange('highValue', e.target.checked)}
                    className="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
                    disabled={loading}
                  />
                  <label htmlFor="highValue" className="text-sm font-medium text-gray-700">
                    <DollarSign className="w-4 h-4 inline mr-1" />
                    High Value (>R1M)
                  </label>
                </div>
              </div>
            </div>
          </div>

          {/* Smart Filters */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">🧠 Smart Filters</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
              {[
                { label: '🔥 Closing Soon (< 7 days)', filter: 'closingSoon' },
                { label: '🆕 Recently Published', filter: 'recentlyPublished' },
                { label: '💎 High Success Rate', filter: 'highSuccessRate' },
                { label: '⚡ Quick Wins', filter: 'quickWins' },
                { label: '🏢 Government Only', filter: 'governmentOnly' },
                { label: '🏭 Private Sector', filter: 'privateSector' },
                { label: '🌍 International', filter: 'international' },
                { label: '🔒 Security Clearance', filter: 'securityClearance' }
              ].map((smartFilter) => (
                <button
                  key={smartFilter.filter}
                  onClick={() => handleFilterChange(smartFilter.filter, !localFilters[smartFilter.filter as keyof TenderFilters])}
                  className={`p-3 text-left text-sm rounded-lg border-2 transition-all duration-200 ${
                    localFilters[smartFilter.filter as keyof TenderFilters]
                      ? 'border-blue-500 bg-blue-50 text-blue-700'
                      : 'border-gray-200 hover:border-blue-300 hover:bg-blue-25 text-gray-700'
                  }`}
                  disabled={loading}
                >
                  {smartFilter.label}
                </button>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Active Filters Display */}
      {activeFiltersCount > 0 && (
        <div className="flex flex-wrap gap-2 mt-4">
          {localFilters.opportunityType && localFilters.opportunityType !== 'all' && (
            <Badge variant="outline" className="flex items-center space-x-1 bg-blue-50 text-blue-700 border-blue-200">
              <span>Type: {filterOptions.opportunityTypes.find(t => t.value === localFilters.opportunityType)?.label || localFilters.opportunityType}</span>
              <X
                className="w-3 h-3 cursor-pointer hover:text-red-500"
                onClick={() => handleFilterChange('opportunityType', 'all')}
              />
            </Badge>
          )}

          {localFilters.search && (
            <Badge variant="outline" className="flex items-center space-x-1">
              <span>Search: "{localFilters.search}"</span>
              <X
                className="w-3 h-3 cursor-pointer hover:text-red-500"
                onClick={() => handleFilterChange('search', '')}
              />
            </Badge>
          )}

          {localFilters.province && (
            <Badge variant="outline" className="flex items-center space-x-1">
              <span>Province: {localFilters.province}</span>
              <X
                className="w-3 h-3 cursor-pointer hover:text-red-500"
                onClick={() => handleFilterChange('province', '')}
              />
            </Badge>
          )}
          
          {localFilters.category && (
            <Badge variant="outline" className="flex items-center space-x-1">
              <span>Category: {localFilters.category}</span>
              <X
                className="w-3 h-3 cursor-pointer hover:text-red-500"
                onClick={() => handleFilterChange('category', '')}
              />
            </Badge>
          )}

          {localFilters.status && (
            <Badge variant="outline" className="flex items-center space-x-1">
              <span>Status: {localFilters.status}</span>
              <X
                className="w-3 h-3 cursor-pointer hover:text-red-500"
                onClick={() => handleFilterChange('status', '')}
              />
            </Badge>
          )}

          {localFilters.industry && (
            <Badge variant="outline" className="flex items-center space-x-1">
              <span>Industry: {localFilters.industry}</span>
              <X
                className="w-3 h-3 cursor-pointer hover:text-red-500"
                onClick={() => handleFilterChange('industry', '')}
              />
            </Badge>
          )}

          {localFilters.tenderType && (
            <Badge variant="outline" className="flex items-center space-x-1">
              <span>Type: {localFilters.tenderType}</span>
              <X
                className="w-3 h-3 cursor-pointer hover:text-red-500"
                onClick={() => handleFilterChange('tenderType', '')}
              />
            </Badge>
          )}

          {localFilters.valueRange && (
            <Badge variant="outline" className="flex items-center space-x-1">
              <span>Value: {localFilters.valueRange}</span>
              <X
                className="w-3 h-3 cursor-pointer hover:text-red-500"
                onClick={() => handleFilterChange('valueRange', '')}
              />
            </Badge>
          )}

          {localFilters.urgencyLevel && (
            <Badge variant="outline" className="flex items-center space-x-1">
              <span>Urgency: {localFilters.urgencyLevel}</span>
              <X
                className="w-3 h-3 cursor-pointer hover:text-red-500"
                onClick={() => handleFilterChange('urgencyLevel', '')}
              />
            </Badge>
          )}

          {localFilters.cidbRequired && (
            <Badge variant="outline" className="flex items-center space-x-1 bg-blue-50 text-blue-700">
              <span>CIDB Required</span>
              <X
                className="w-3 h-3 cursor-pointer hover:text-red-500"
                onClick={() => handleFilterChange('cidbRequired', false)}
              />
            </Badge>
          )}

          {localFilters.beeRequired && (
            <Badge variant="outline" className="flex items-center space-x-1 bg-green-50 text-green-700">
              <span>BEE Required</span>
              <X
                className="w-3 h-3 cursor-pointer hover:text-red-500"
                onClick={() => handleFilterChange('beeRequired', false)}
              />
            </Badge>
          )}

          {localFilters.minValue && (
            <Badge variant="outline" className="flex items-center space-x-1">
              <span>Min: R{localFilters.minValue.toLocaleString()}</span>
              <X
                className="w-3 h-3 cursor-pointer hover:text-red-500"
                onClick={() => handleFilterChange('minValue', undefined)}
              />
            </Badge>
          )}

          {localFilters.maxValue && (
            <Badge variant="outline" className="flex items-center space-x-1">
              <span>Max: R{localFilters.maxValue.toLocaleString()}</span>
              <X
                className="w-3 h-3 cursor-pointer hover:text-red-500"
                onClick={() => handleFilterChange('maxValue', undefined)}
              />
            </Badge>
          )}
        </div>
      )}
    </div>
  )
}
