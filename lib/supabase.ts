import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://uvksgkpxeyyssvdsxbts.supabase.co'
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV2a3Nna3B4ZXl5c3N2ZHN4YnRzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYwMzU4NDIsImV4cCI6MjA2MTYxMTg0Mn0.AF1fLlULlM-_NUJYFEL092WETAXvpKKpawUsOidHQ70'

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    persistSession: false,
  },
})

// Debug logging
console.log('🔧 Supabase client initialized:', {
  url: supabaseUrl,
  keyLength: supabaseAnonKey?.length,
  hasKey: !!supabaseAnonKey
})

// Database types
export interface TenderRecord {
  id: string
  tender_number?: string
  title: string
  description?: string
  issuer_name?: string
  issuer_type?: string
  publish_date?: string
  closing_date?: string
  category_code?: string
  cpv_code?: string
  document_available?: boolean
  document_link?: string
  documents_uploaded?: boolean
  document_price?: number
  risk_score?: number
  institution_risk_score?: number
  success_prediction?: number
  parsed_by?: string
  original_data?: any
  scm_contact_name?: string
  scm_contact_number?: string
  technical_enquiries_name?: string
  technical_enquiries_contact?: string
  province?: string
  city?: string
  created_at?: string
  updated_at?: string
  tender_value?: number
  currency?: string
  procurement_method?: string
  procurement_method_details?: string
  release_type?: string

  // Site Meeting Details
  site_meeting_required?: boolean
  site_meeting_date?: string
  site_meeting_time?: string
  site_meeting_location?: string
  site_meeting_address?: string
  site_meeting_contact_person?: string
  site_meeting_contact_number?: string
  site_meeting_contact_email?: string
  site_meeting_instructions?: string
  site_meeting_mandatory?: boolean
  site_meeting_registration_required?: boolean
  site_meeting_registration_deadline?: string
  site_meeting_maximum_attendees?: number
  site_meeting_documents_provided?: boolean
  site_meeting_notes?: string
  site_meeting_coordinates?: any
  site_meeting_parking_available?: boolean
  site_meeting_parking_instructions?: string
  site_meeting_security_requirements?: string
  site_meeting_dress_code?: string

  // Skills and Team Requirements
  skills_required?: Array<{
    skill: string
    level: 'basic' | 'intermediate' | 'advanced' | 'expert'
    mandatory: boolean
    years_experience?: number
    certification_required?: boolean
  }>
  skills_preferred?: Array<{
    skill: string
    level: 'basic' | 'intermediate' | 'advanced' | 'expert'
    mandatory: boolean
    years_experience?: number
    certification_required?: boolean
  }>
  minimum_experience_years?: number
  required_qualifications?: Array<{
    qualification: string
    level: string
    mandatory: boolean
    institution_accredited?: boolean
  }>
  preferred_qualifications?: Array<{
    qualification: string
    level: string
    mandatory: boolean
    institution_accredited?: boolean
  }>
  required_certifications?: Array<{
    certification: string
    issuing_body: string
    mandatory: boolean
    expiry_required?: boolean
  }>
  team_composition_required?: Array<{
    role: string
    count: number
    skills: string[]
    experience_years: number
  }>
  key_personnel_required?: Array<{
    role: string
    name?: string
    skills: string[]
    experience_years: number
    cv_required: boolean
  }>

  // BEE Requirements
  bee_requirements?: {
    level_required?: string
    ownership_percentage?: number
    women_participation?: number
    youth_participation?: number
    disabled_participation?: number
    local_content_percentage?: number
    subcontracting_requirements?: string
    verification_required?: boolean
    certificate_validity_months?: number
  }

  // SkillSync Integration
  skillsync_integration_enabled?: boolean
  skillsync_job_categories?: Array<{
    category: string
    subcategory?: string
    priority: 'high' | 'medium' | 'low'
  }>
  skillsync_matching_criteria?: {
    minimum_match_percentage?: number
    preferred_location_radius?: number
    availability_required?: boolean
    rate_range?: {
      min: number
      max: number
      currency: string
    }
  }

  // System Integration Status
  erp_sync_status?: 'pending' | 'synced' | 'failed' | 'disabled'
  erp_project_id?: string
  crm_opportunity_id?: string
  accounting_sync_status?: 'pending' | 'synced' | 'failed' | 'disabled'
  external_system_refs?: {
    [system: string]: {
      id: string
      status: string
      last_sync?: string
      sync_errors?: string[]
    }
  }
  should_include_in_main?: boolean
  scm_contact_email?: string
  technical_enquiries_email?: string
  address?: string
  delivery_address?: string
  etenders_id?: string
  etenders_url?: string
  etenders_status?: string
  etenders_last_updated?: string
  etenders_scraped_at?: string
  etenders_session_id?: string
  etenders_department_code?: string
  etenders_category_id?: string
  etenders_priority?: number
  etenders_processing_status?: string
  etenders_error_message?: string
  etenders_retry_count?: number
  etenders_metadata?: any
  main_procurement_category?: string
  // AI-generated fields
  aiScore?: number
  aiInsights?: string[]
  // Add other fields as needed
}

export interface TenderFilters {
  search?: string
  province?: string
  category?: string
  status?: string
  minValue?: number
  maxValue?: number
  closingDateFrom?: string
  closingDateTo?: string
  publishDateFrom?: string
  publishDateTo?: string
  siteMeetingDateFrom?: string
  siteMeetingDateTo?: string
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
  // BidBeez Core Classification
  opportunityType?: 'all' | 'rfq' | 'tender' | 'direct_appointment'
  // Enhanced filters
  tenderType?: string
  procurementMethod?: string
  issuerType?: string
  urgencyLevel?: string
  cidbRequired?: boolean
  beeRequired?: boolean
  securityClearance?: boolean
  valueRange?: string
  industry?: string
  geographicScope?: string
  documentStatus?: string
  // Advanced search options
  siteMeetingRequired?: boolean
  documentAvailable?: boolean
  highValue?: boolean
  // Smart filters
  closingSoon?: boolean
  recentlyPublished?: boolean
  highSuccessRate?: boolean
  quickWins?: boolean
  governmentOnly?: boolean
  privateSector?: boolean
  international?: boolean
}

export interface TenderStats {
  total: number
  active: number
  closed: number
  awarded: number
  liveOpportunities: number
  rfqCount: number
  byProvince: Record<string, number>
  byCategory: Record<string, number>
  totalValue: number
  averageValue: number
}

// API functions
export const tenderApi = {
  // Get tenders with filtering and pagination
  getTenders: async (
    filters: TenderFilters = {},
    page = 1,
    limit = 20
  ): Promise<{ data: TenderRecord[]; count: number; error?: string }> => {
    try {
      console.log('🔍 getTenders called with:', { filters, page, limit })
      
      let query = supabase
        .from('tenders')
        .select('*', { count: 'exact' })
        // Remove overly restrictive filters to show available data
        // .like('ocid', 'ocds-9t57fa-%')  // Only OCDS records
        // .eq('etenders_processing_status', 'processed_real_data')  // Only real eTenders data

      console.log('🔧 Initial query created')

      // Apply filters
      if (filters.search) {
        query = query.or(`title.ilike.%${filters.search}%,description.ilike.%${filters.search}%,issuer_name.ilike.%${filters.search}%,tender_number.ilike.%${filters.search}%`)
      }

      // BidBeez Core Classification Filter
      if (filters.opportunityType && filters.opportunityType !== 'all') {
        if (filters.opportunityType === 'rfq') {
          query = query.eq('release_type', 'quotation')
        } else if (filters.opportunityType === 'tender') {
          query = query.eq('release_type', 'open_tender')
        } else if (filters.opportunityType === 'direct_appointment') {
          query = query.eq('release_type', 'direct_appointment')
        }
      }

      if (filters.province) {
        query = query.eq('province', filters.province)
      }

      if (filters.category) {
        query = query.eq('main_procurement_category', filters.category)
      }

      // Date-based status filtering - handle null closing dates
      if (filters.status && filters.status !== 'All') {
        if (filters.status === 'Live/Open') {
          // Include records with null closing_date (ongoing opportunities) OR future closing dates
          query = query.or('closing_date.is.null,closing_date.gt.' + new Date().toISOString())
        } else if (filters.status === 'Closed') {
          // Only records with past closing dates (exclude nulls)
          query = query.lte('closing_date', new Date().toISOString()).not('closing_date', 'is', null)
        } else if (filters.status === 'Closing Soon') {
          const sevenDaysFromNow = new Date()
          sevenDaysFromNow.setDate(sevenDaysFromNow.getDate() + 7)
          query = query
            .gt('closing_date', new Date().toISOString())
            .lte('closing_date', sevenDaysFromNow.toISOString())
            .not('closing_date', 'is', null)
        }
      }
      // 'All' status doesn't add any filter - shows all records

      if (filters.minValue) {
        query = query.gte('tender_value', filters.minValue)
      }

      if (filters.maxValue) {
        query = query.lte('tender_value', filters.maxValue)
      }

      if (filters.closingDateFrom) {
        query = query.gte('closing_date', filters.closingDateFrom)
      }

      if (filters.closingDateTo) {
        query = query.lte('closing_date', filters.closingDateTo)
      }

      // Apply sorting - Default to closing_date for better user experience
      const sortBy = filters.sortBy || 'closing_date'
      const sortOrder = filters.sortOrder || 'asc'  // Ascending for closing dates (soonest first)

      // Handle null closing dates by putting them last
      if (sortBy === 'closing_date') {
        query = query.order(sortBy, { ascending: sortOrder === 'asc', nullsFirst: false })
      } else {
        query = query.order(sortBy, { ascending: sortOrder === 'asc' })
      }

      // Apply pagination
      const from = (page - 1) * limit
      const to = from + limit - 1
      query = query.range(from, to)

      const { data, error, count } = await query

      console.log('📊 Query results:', {
        dataLength: data?.length,
        count,
        error: error?.message,
        hasData: !!data
      })

      if (error) {
        console.error('❌ Error fetching tenders:', error)
        return { data: [], count: 0, error: error.message }
      }

      console.log('✅ Returning data:', { dataCount: data?.length, totalCount: count })
      return { data: data || [], count: count || 0 }
    } catch (error) {
      console.error('Error in getTenders:', error)
      return { data: [], count: 0, error: 'Failed to fetch tenders' }
    }
  },

  // Get a single tender by ID
  getTenderById: async (id: string): Promise<{ data: TenderRecord | null; error?: string }> => {
    try {
      const { data, error } = await supabase
        .from('tenders')
        .select('*')
        .eq('id', id)
        .single()

      if (error) {
        console.error('Error fetching tender by ID:', error)
        return { data: null, error: error.message }
      }

      return { data }
    } catch (error) {
      console.error('Error in getTenderById:', error)
      return { data: null, error: 'Failed to fetch tender' }
    }
  },

  // Get tender statistics
  getTenderStats: async (): Promise<{ data: TenderStats | null; error?: string }> => {
    try {
      console.log('📊 getTenderStats called')
      
      // Get total count - show all available data
      const { count: total } = await supabase
        .from('tenders')
        .select('*', { count: 'exact', head: true })
        // Remove restrictive filters to show available data
        // .like('ocid', 'ocds-9t57fa-%')
        // .eq('etenders_processing_status', 'processed_real_data')

      console.log('📈 Total count result:', total)

      // Get active tenders (null closing dates OR future dates)
      const { count: active } = await supabase
        .from('tenders')
        .select('*', { count: 'exact', head: true })
        // .like('ocid', 'ocds-9t57fa-%')
        // .eq('etenders_processing_status', 'processed_real_data')
        .or('closing_date.is.null,closing_date.gte.' + new Date().toISOString().split('T')[0])

      // Get closed tenders (only past dates, exclude nulls)
      const { count: closed } = await supabase
        .from('tenders')
        .select('*', { count: 'exact', head: true })
        // .like('ocid', 'ocds-9t57fa-%')
        // .eq('etenders_processing_status', 'processed_real_data')
        .lt('closing_date', new Date().toISOString().split('T')[0])
        .not('closing_date', 'is', null)

      // Get awarded tenders
      const { count: awarded } = await supabase
        .from('tenders')
        .select('*', { count: 'exact', head: true })
        .not('etenders_status', 'is', null)
        .eq('etenders_status', 'awarded')

      // Get province distribution
      const { data: provinceData } = await supabase
        .from('tenders')
        .select('province')
        .not('province', 'is', null)

      const byProvince: Record<string, number> = {}
      provinceData?.forEach(item => {
        if (item.province) {
          byProvince[item.province] = (byProvince[item.province] || 0) + 1
        }
      })

      // Get category distribution
      const { data: categoryData } = await supabase
        .from('tenders')
        .select('main_procurement_category')
        .not('main_procurement_category', 'is', null)

      const byCategory: Record<string, number> = {}
      categoryData?.forEach(item => {
        if (item.main_procurement_category) {
          byCategory[item.main_procurement_category] = (byCategory[item.main_procurement_category] || 0) + 1
        }
      })

      // Get value statistics
      const { data: valueData } = await supabase
        .from('tenders')
        .select('tender_value')
        .not('tender_value', 'is', null)

      const totalValue = valueData?.reduce((sum, item) => sum + (item.tender_value || 0), 0) || 0
      const averageValue = valueData?.length ? totalValue / valueData.length : 0

      // Get RFQ count (quotation type)
      const { count: rfqCount } = await supabase
        .from('tenders')
        .select('*', { count: 'exact', head: true })
        .eq('release_type', 'quotation')
        .or('closing_date.is.null,closing_date.gte.' + new Date().toISOString().split('T')[0])

      const stats: TenderStats = {
        total: total || 0,
        active: active || 0,
        closed: closed || 0,
        awarded: awarded || 0,
        liveOpportunities: active || 0, // Use active count for live opportunities
        rfqCount: rfqCount || 0,
        byProvince,
        byCategory,
        totalValue,
        averageValue
      }

      return { data: stats }
    } catch (error) {
      console.error('Error getting tender stats:', error)
      return { data: null, error: 'Failed to fetch statistics' }
    }
  },

  // Get filter options
  getFilterOptions: async (): Promise<{
    provinces: string[]
    categories: string[]
    statuses: string[]
    opportunityTypes: { value: string; label: string; count: number; description: string }[]
    tenderTypes: string[]
    procurementMethods: string[]
    issuerTypes: string[]
    urgencyLevels: string[]
    valueRanges: { label: string; min: number; max?: number }[]
    industries: string[]
    geographicScopes: string[]
    documentStatuses: string[]
  }> => {
    try {
      // Get unique provinces (with data cleaning)
      const { data: provinceData } = await supabase
        .from('tenders')
        .select('province')
        // Remove restrictive filters to show available data
        // .like('ocid', 'ocds-9t57fa-%')
        // .eq('etenders_processing_status', 'processed_real_data')
        .not('province', 'is', null)
        .order('province')

      // Process province data for filter options
      const rawProvinces = Array.from(new Set(provinceData?.map(p => p.province).filter(Boolean))) as string[]

      // Clean and standardize province names
      const provinceMapping: Record<string, string> = {
        'Kwazulu-Natal': 'KwaZulu-Natal',
        'kwazulu-natal': 'KwaZulu-Natal',
        'Unknown': 'National'
      }

      const cleanedProvinces = rawProvinces.map(p => provinceMapping[p] || p)

      // All SA provinces (always include these)
      const allSAProvinces = [
        'Eastern Cape',
        'Free State',
        'Gauteng',
        'KwaZulu-Natal',
        'Limpopo',
        'Mpumalanga',
        'Northern Cape',
        'North West',
        'Western Cape',
        'National'
      ]

      // Include all SA provinces, plus any additional ones from the data
      const additionalProvinces = cleanedProvinces.filter(p => !allSAProvinces.includes(p))
      const provinces = [...allSAProvinces, ...additionalProvinces]

      // Get unique categories (enhanced)
      const { data: categoryData } = await supabase
        .from('tenders')
        .select('main_procurement_category')
        .not('main_procurement_category', 'is', null)
        .order('main_procurement_category')

      const rawCategories = Array.from(new Set(categoryData?.map(c => c.main_procurement_category).filter(Boolean))) as string[]
      const categories = rawCategories.filter(c => c.trim() !== '')

      // Real status options based on closing dates and actual data
      const statuses = [
        'Live/Open',      // closing_date > NOW() - can still bid
        'Closed',         // closing_date <= NOW() - bidding closed
        'Closing Soon',   // closing_date within 7 days
        'All'            // show all regardless of status
      ]

      // Tender types
      const tenderTypes = [
        'Open Tender',
        'Restricted Tender',
        'Negotiated Procedure',
        'Framework Agreement',
        'Dynamic Purchasing System',
        'Innovation Partnership',
        'Competitive Dialogue',
        'Request for Quotation'
      ]

      // Procurement methods
      const procurementMethods = [
        'Public Tender',
        'Restricted Tender',
        'Negotiated Procedure',
        'Competitive Dialogue',
        'Innovation Partnership',
        'Framework Agreement',
        'Direct Award'
      ]

      // Issuer types
      const issuerTypes = [
        'National Government',
        'Provincial Government',
        'Local Government',
        'State-Owned Enterprise',
        'Public Entity',
        'Municipality',
        'University',
        'Hospital',
        'School'
      ]

      // Urgency levels
      const urgencyLevels = [
        'Critical (< 7 days)',
        'Urgent (< 14 days)',
        'Normal (< 30 days)',
        'Extended (> 30 days)'
      ]

      // Value ranges
      const valueRanges = [
        { label: 'Under R100K', min: 0, max: 100000 },
        { label: 'R100K - R500K', min: 100000, max: 500000 },
        { label: 'R500K - R1M', min: 500000, max: 1000000 },
        { label: 'R1M - R5M', min: 1000000, max: 5000000 },
        { label: 'R5M - R10M', min: 5000000, max: 10000000 },
        { label: 'R10M - R50M', min: 10000000, max: 50000000 },
        { label: 'Over R50M', min: 50000000 }
      ]

      // Industries
      const industries = [
        'Information Technology',
        'Construction & Infrastructure',
        'Healthcare & Medical',
        'Education & Training',
        'Professional Services',
        'Security Services',
        'Cleaning & Maintenance',
        'Catering & Food Services',
        'Transport & Logistics',
        'Energy & Utilities',
        'Agriculture',
        'Manufacturing',
        'Telecommunications',
        'Financial Services',
        'Legal Services'
      ]

      // Geographic scopes
      const geographicScopes = [
        'National',
        'Provincial',
        'Municipal',
        'District',
        'Local',
        'Cross-Border'
      ]

      // Document statuses
      const documentStatuses = [
        'Available',
        'Pending',
        'Restricted Access',
        'Download Required',
        'Registration Required'
      ]

      // BidBeez Core Opportunity Types with real counts
      const { data: releaseTypeCounts } = await supabase
        .from('tenders')
        .select('release_type')
        .not('release_type', 'is', null)

      const typeCounts = releaseTypeCounts?.reduce((acc: Record<string, number>, item) => {
        acc[item.release_type] = (acc[item.release_type] || 0) + 1
        return acc
      }, {}) || {}

      const opportunityTypes = [
        {
          value: 'all',
          label: 'All Opportunities',
          count: (typeCounts['quotation'] || 0) + (typeCounts['open_tender'] || 0) + (typeCounts['direct_appointment'] || 0),
          description: 'All types of procurement opportunities'
        },
        {
          value: 'rfq',
          label: 'RFQs (Request for Quotations)',
          count: typeCounts['quotation'] || 0,
          description: 'Low hanging fruits - Quick turnaround, smaller value opportunities perfect for getting started'
        },
        {
          value: 'tender',
          label: 'Formal Tenders',
          count: typeCounts['open_tender'] || 0,
          description: 'Traditional competitive tenders - Higher value, longer timeline, more complex requirements'
        },
        {
          value: 'direct_appointment',
          label: 'Direct Appointments',
          count: typeCounts['direct_appointment'] || 0,
          description: 'Non-competitive direct appointments and deviations'
        }
      ]

      return {
        provinces,
        categories,
        statuses,
        opportunityTypes,
        tenderTypes,
        procurementMethods,
        issuerTypes,
        urgencyLevels,
        valueRanges,
        industries,
        geographicScopes,
        documentStatuses
      }
    } catch (error) {
      console.error('Error getting filter options:', error)
      return {
        provinces: [],
        categories: [],
        statuses: [],
        opportunityTypes: [],
        tenderTypes: [],
        procurementMethods: [],
        issuerTypes: [],
        urgencyLevels: [],
        valueRanges: [],
        industries: [],
        geographicScopes: [],
        documentStatuses: []
      }
    }
  }
}

export default supabase
